package com.socialplay.gpark.util.extension

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.GradientDrawable
import androidx.annotation.ColorInt
import androidx.lifecycle.LifecycleOwner
import com.bin.cpbus.CpEventBus
import com.luck.picture.lib.entity.LocalMedia
import com.meta.biz.mgs.data.model.MgsPlayerInfo
import com.meta.box.biz.friend.model.FriendInfo
import com.meta.box.biz.friend.model.FriendRequestInfo
import com.meta.lib.mwbiz.MWBizBridge
import com.meta.lib.mwbiz.bean.game.GameResumeParams
import kotlinx.coroutines.Job
import org.greenrobot.eventbus.EventBus
import timber.log.Timber

/**
 * <pre>
 * author : xuanxin
 * e-mail : <EMAIL>
 * time   : 2021/09/29
 * desc   :
 * </pre>
 */

inline fun tryWaite(ms: Long, interval: Long, crossinline block: () -> Boolean) {
    for (i in 0..ms step interval) {
        Timber.d("LaunchGameInteractor tryWaite i:$i")
        if (runCatching(block).getOrDefault(false)) {
            return
        }
        runCatching { Thread.sleep(interval) }
    }
}

fun <T> Lazy<T>.orNull(): T? {
    return if (isInitialized()) {
        value
    } else {
        null
    }
}

val LocalMedia.likelyPath get(): String? = compressPath ?: realPath ?: path

fun <T> List<T>?.getAt(index: Int, predicate: (T) -> Boolean): T? {
    return getWithIndex(index, predicate)?.second
}

fun <T> List<T>?.getWithIndex(index: Int, predicate: (T) -> Boolean): Pair<Int, T>? {
    return if (this.isNullOrEmpty()) {
        null
    } else {
        kotlin.runCatching {
            if (index in indices && predicate(get(index))) {
                index to get(index)
            } else {
                val curIndex = indexOfFirst(predicate)
                if (curIndex == -1) {
                    null
                } else {
                    curIndex to get(curIndex)
                }
            }
        }.getOrNull()
    }
}

fun <T> List<T>?.getWithIndex(predicate: (T) -> Boolean): Pair<Int, T>? {
    return if (this.isNullOrEmpty()) {
        null
    } else {
        kotlin.runCatching {
            val curIndex = indexOfFirst(predicate)
            if (curIndex == -1) {
                null
            } else {
                curIndex to get(curIndex)
            }
        }.getOrNull()
    }
}

/**
 * Returns a new [ArrayList] filled with all elements of this collection.
 */
fun <T> Collection<T>.toArrayList(): ArrayList<T> {
    return ArrayList(this)
}

fun <T> List<T>?.dropAt(index: Int): List<T>? {
    return dropAtWithResult(index).second
}

fun <T> List<T>?.dropAtWithResult(index: Int): Triple<Boolean, List<T>?, T?> {
    return if (this.isNullOrEmpty()) {
        Triple(false, this, null)
    } else {
        kotlin.runCatching {
            val curIndex = if (index in indices) {
                index
            } else {
                -1
            }
            val resultList = when (curIndex) {
                -1 -> {
                    this
                }

                0 -> {
                    subList(1, size)
                }

                size - 1 -> {
                    subList(0, size - 1)
                }

                else -> {
                    subList(0, curIndex) + subList(curIndex + 1, size)
                }
            }
            val item = when (curIndex) {
                -1 -> null
                else -> getOrNull(curIndex)
            }
            Triple(curIndex != -1, resultList, item)
        }.getOrDefault(Triple(false, this, null))
    }
}

fun <T> List<T>?.dropAt(index: Int, item: T): List<T>? {
    return dropAtWithResult(index, item).third
}

fun <T> List<T>?.dropAtWithResult(index: Int, item: T): Triple<Boolean, Int, List<T>?> {
    return if (this.isNullOrEmpty()) {
        Triple(false, -1, this)
    } else {
        kotlin.runCatching {
            val curIndex = if (index in indices && get(index) == item) {
                index
            } else {
                indexOf(item)
            }
            val resultList = when (curIndex) {
                -1 -> {
                    this
                }

                0 -> {
                    subList(1, size)
                }

                size - 1 -> {
                    subList(0, size - 1)
                }

                else -> {
                    subList(0, curIndex) + subList(curIndex + 1, size)
                }
            }
            Triple(curIndex != -1, curIndex, resultList)
        }.getOrDefault(Triple(false, -1, this))
    }
}

/**
 * [from] inclusive, [to] inclusive
 */
fun <T> List<T>?.dropRange(from: Int, to: Int): List<T>? {
    return kotlin.runCatching {
        if (this.isNullOrEmpty()) {
            this
        } else {
            val okFrom = from.coerceIn(indices)
            val okTo = to.coerceIn(indices)
            if (okFrom > 0) {
                if (okTo == size - 1) {
                    subList(0, okFrom)
                } else {
                    subList(0, okFrom) + subList(okTo + 1, size)
                }
            } else {
                if (okTo == size - 1) {
                    emptyList()
                } else {
                    subList(okTo + 1, size)
                }
            }
        }
    }.getOrDefault(this)
}

fun <T> List<T>?.dropSingle(predicate: (T) -> Boolean): Pair<Boolean, List<T>?> {
    return if (this.isNullOrEmpty()) {
        false to this
    } else {
        val index = indexOfFirst(predicate)
        if (index != -1) {
            true to dropAt(index, get(index))
        } else {
            false to this
        }
    }
}

fun <T> List<T>?.dropSingle(old: T): Pair<Boolean, List<T>?> {
    return if (this.isNullOrEmpty()) {
        false to this
    } else {
        val index = indexOf(old)
        if (index != -1) {
            true to dropAt(index, old)
        } else {
            false to this
        }
    }
}

fun <T> List<T>?.replaceAt(index: Int, vararg items: T): List<T>? {
    return replaceRange(index, index, items = items)
}

/**
 * [from] inclusive, [to] inclusive
 */
fun <T> List<T>?.replaceRange(from: Int, to: Int, vararg items: T): List<T>? {
    return replaceRange(from, to, items.asList())
}

fun <T> List<T>?.replaceRange(from: Int, to: Int, items: List<T>?): List<T>? {
    return kotlin.runCatching {
        if (this.isNullOrEmpty()) {
            items
        } else if (items.isNullOrEmpty()) {
            dropRange(from, to)
        } else {
            val okFrom = from.coerceIn(indices)
            val okTo = to.coerceIn(indices)
            if (okFrom > 0) {
                if (okTo == size - 1) {
                    subList(0, okFrom) + items
                } else {
                    subList(0, okFrom) + items + subList(okTo + 1, size)
                }
            } else {
                if (okTo == size - 1) {
                    items
                } else {
                    items + subList(okTo + 1, size)
                }
            }
        }
    }.getOrDefault(this)
}

fun <T> List<T>?.replaceSingle(
    predicate: (T) -> Boolean,
    transform: (T) -> T
): Pair<Boolean, List<T>?> {
    return if (this.isNullOrEmpty()) {
        false to this
    } else {
        val index = indexOfFirst(predicate)
        if (index != -1) {
            true to replaceAt(index, transform(get(index)))
        } else {
            false to this
        }
    }
}

fun <T> List<T>?.replaceSingle(old: T, new: T): Pair<Boolean, List<T>?> {
    return if (this.isNullOrEmpty()) {
        false to this
    } else {
        val index = indexOf(old)
        if (index != -1) {
            true to replaceAt(index, new)
        } else {
            false to this
        }
    }
}

fun <T> List<T>?.insertAt(index: Int, vararg items: T): List<T>? {
    return insertAt(index, items.asList())
}

fun <T> List<T>?.insertAt(index: Int, items: List<T>?): List<T>? {
    return kotlin.runCatching {
        if (this.isNullOrEmpty()) {
            items
        } else if (items.isNullOrEmpty()) {
            this
        } else {
            when (index) {
                0 -> {
                    items + this
                }

                size, -1 -> {
                    this + items
                }

                else -> {
                    subList(0, index) + items + subList(index, size)
                }
            }
        }
    }.getOrDefault(this)
}

/**
 * [from] inclusive, [to] inclusive
 */
fun <T> List<T>?.moveTo(from: Int, to: Int): List<T>? {
    return if (isNullOrEmpty() || from == to) {
        this
    } else {
        val indices = indices
        val okFrom = from.coerceIn(indices)
        val okTo = to.coerceIn(indices)
        val (ok, dropList, item) = dropAtWithResult(okFrom)
        return if (ok && item != null) {
            dropList.insertAt(okTo, item)
        } else {
            this
        }
    }
}

/**
 * [from] inclusive, [to] inclusive
 */
fun <T> List<T>?.moveOldTo(from: Int, to: Int, oldItem: T): List<T>? {
    return moveNewTo(from, to, oldItem, oldItem)
}

fun <T> List<T>?.moveNewTo(from: Int, to: Int, oldItem: T, newItem: T): List<T>? {
    return kotlin.runCatching {
        if (this.isNullOrEmpty()) {
            listOf(newItem)
        } else {
            val okFrom = from.coerceIn(indices)
            val okTo = to.coerceIn(indices)
            val (dropOk, _, dropList) = dropAtWithResult(okFrom, oldItem)
            if (!dropOk || dropList == null) {
                this
            } else {
                dropList.insertAt(okTo, newItem)
            }
        }
    }.getOrDefault(this)
}

fun <T> List<T>?.addAllImmutable(items: List<T>?): List<T>? {
    return kotlin.runCatching {
        if (items.isNullOrEmpty()) {
            this
        } else if (this.isNullOrEmpty()) {
            items
        } else {
            this + items
        }
    }.getOrDefault(this)
}

fun Any.registerHermes() {
    CpEventBus.register(this)
}

fun Any.unregisterHermes() {
    CpEventBus.unregister(this)
}

fun Any.registerEventBus() {
    EventBus.getDefault().register(this)
}

fun Any.unregisterEventBus() {
    EventBus.getDefault().unregister(this)
}

fun Any.registerHermes(owner: LifecycleOwner) {
    owner.observeOnMainThreadWhenNotDestroyed(
        register = {
            registerHermes()
        },
        unregister = {
            unregisterHermes()
        }
    )
}

val FriendInfo.tagIds get() = tags?.map { it.id }
val FriendRequestInfo.tagIds get() = tags?.map { it.id }
val MgsPlayerInfo.tagIds get() = tags?.map { it.id }

fun resumeGameById(gameId: String) {
    MWBizBridge.resumeGame(gameId, GameResumeParams(useI18n = true))
}

fun @receiver:ColorInt Int.toGradientDrawable(cornerRadius: Float = 0f): GradientDrawable {
    return GradientDrawable().apply {
        shape = GradientDrawable.RECTANGLE
        setColor(this@toGradientDrawable)
        this.cornerRadius = cornerRadius
    }
}