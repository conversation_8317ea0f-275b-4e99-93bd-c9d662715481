package com.socialplay.gpark.ui.core

import android.content.Context
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.LayoutRes
import androidx.core.widget.doAfterTextChanged
import androidx.viewbinding.ViewBinding
import com.airbnb.epoxy.EpoxyModel
import com.bumptech.glide.Glide
import com.bumptech.glide.RequestManager
import com.socialplay.gpark.R
import com.socialplay.gpark.util.extension.dp
import com.socialplay.gpark.util.extension.setMargin
import com.socialplay.gpark.util.extension.setOnAntiViolenceClickListener
import com.socialplay.gpark.util.extension.setPaddingEx
import com.socialplay.gpark.util.property.getBindMethodFrom

abstract class BaseItemEpoxyModel<T, V : View>(@LayoutRes private val layoutRes: Int) :
    EpoxyModel<V>() {

    protected abstract fun createTarget(view: V): T

    protected abstract fun getTarget(view: V): T?

    protected abstract fun T.getItemView(): View

    protected abstract fun T.onBind()

    protected open fun T.onBind(payloads: MutableList<Any>) {
        onBind()
    }

    protected open fun T.onBind(previouslyBoundModel: EpoxyModel<*>) {
        onBind()
    }

    protected open fun T.onUnbind() {}

    final override fun bind(view: V) {

        super.bind(view)
        (getTarget(view) ?: createTarget(view)).also { target ->
            target.onBind()
        }
    }

    final override fun unbind(view: V) {
        super.unbind(view)
        getTarget(view)?.also { target ->
            target.onUnbind()
        }
    }

    final override fun bind(view: V, payloads: MutableList<Any>) {
        super.bind(view, payloads)
        (getTarget(view) ?: createTarget(view)).also { target ->
            target.onBind(payloads)
        }
    }

    final override fun bind(view: V, previouslyBoundModel: EpoxyModel<*>) {
        super.bind(view, previouslyBoundModel)
        (getTarget(view) ?: createTarget(view)).also { target ->
            target.onBind(previouslyBoundModel)
        }
    }

    final override fun layout(layoutRes: Int): EpoxyModel<V> {
        return this
    }

    final override fun getDefaultLayout(): Int {
        return layoutRes
    }

    fun TextView.setOnTextAfterChanged(action: ((text: Editable?) -> Unit)?) {
        val oldWatcher = getTag(R.id.meta_epoxy_model_text_view_text_watcher) as? TextWatcher
        oldWatcher?.let { removeTextChangedListener(it) }
        if (action != null) {
            val newWatcher = doAfterTextChanged(action)
            setTag(R.id.meta_epoxy_model_text_view_text_watcher, newWatcher)
        } else {
            setTag(R.id.meta_epoxy_model_text_view_text_watcher, null)
        }
    }

    final override fun buildView(parent: ViewGroup): View {
        return createView(parent).also { onViewCreated(parent, it) }
    }

    protected open fun onViewCreated(parent: ViewGroup, itemView: View) {}

    protected open fun createView(parent: ViewGroup): View {
        return super.buildView(parent)
    }

    // 感觉效果不大，所以注释了(并且也没有写完，不能无限缓存，还是需要清理的)
//    protected open fun createView(parent: ViewGroup): View {
//        val id = id()
//        ItemViewCache.getCacheView(id)?.let {
//            return it
//        }
//        val buildView = super.buildView(parent)
//        ItemViewCache.putCacheView(id, buildView)
//        return buildView
//    }
}

abstract class BaseItemEpoxyModelWithGlide<T, V : View>(@LayoutRes layoutRes: Int) :
    BaseItemEpoxyModel<T, V>(layoutRes) {

//    override fun onViewCreated(parent: ViewGroup, itemView: View) {
//        super.onViewCreated(parent, itemView)
//        itemView.setTag(R.id.meta_epoxy_model_parent_view, WeakReference(parent))
//    }
//
//    private fun getGlide(itemView: View): RequestManager {
//        val parentView = (itemView.parent as? View)
//            ?: (itemView.getTag(R.id.meta_epoxy_model_parent_view) as? WeakReference<*>)?.get() as? View
//        if (parentView != null) {
//            val requestManager =
//                (parentView.getTag(R.id.meta_epoxy_model_glide_request_manager) as? WeakReference<*>)?.get() as? RequestManager
//            if (requestManager != null) {
//                return requestManager
//            }
//            val result = Glide.with(parentView)
//            parentView.setTag(R.id.meta_epoxy_model_glide_request_manager, WeakReference(result))
//            return result
//        }
//        return Glide.with(itemView)
//    }

    protected fun T.withGlide(): RequestManager {
//        return getGlide(getItemView())
        return Glide.with(getItemView())
    }
}

abstract class ViewBindingItemModel<VB : ViewBinding>(
    @LayoutRes layoutRes: Int,
    private val binder: (View) -> VB
) : BaseItemEpoxyModelWithGlide<VB, View>(layoutRes) {

    override fun VB.getItemView(): View {
        return root
    }

    override fun getTarget(view: View): VB? {
        return view.getTag(R.id.meta_epoxy_model_viewbinding_tag) as? VB
    }

    override fun createTarget(view: View): VB {
        return binder.invoke(view).also {
            view.setTag(R.id.meta_epoxy_model_viewbinding_tag, it)
        }
    }

    /**
     * 一排两个的item，更新左右间距
     */
    protected fun VB.updateHorizontalGap(index: Int, bottom: Int? = null) {
        if (index % 2 == 0) {
            root.setPaddingEx(left = dp(16), right = dp(6), bottom = bottom ?: root.paddingBottom)
        } else {
            root.setPaddingEx(left = dp(6), right = dp(16), bottom = bottom ?: root.paddingBottom)
        }
    }

    /**
     * 一排两个的item，更新左右间距
     */
    protected fun VB.updateHorizontalGapBySpanSize(spanSize: Int, index: Int, bottom: Int? = null) {
        if (index % spanSize == 0) {
            // 头
            root.setMargin(left = dp(16), right = dp(6), bottom = bottom ?: root.paddingBottom)
        } else if (index % spanSize == spanSize - 1) {
            // 尾
            root.setMargin(left = dp(6), right = dp(16), bottom = bottom ?: root.paddingBottom)
        } else {
            root.setMargin(left = dp(6), right = dp(6), bottom = bottom ?: root.paddingBottom)
        }
    }

}

abstract class ViewItemModel<V : View> : BaseItemEpoxyModelWithGlide<V, V>(layoutRes = 0) {

    override fun createTarget(view: V): V = view

    override fun getTarget(view: V): V? = view

    override fun V.getItemView(): View {
        return this
    }

    protected abstract fun createView(parent: ViewGroup, context: Context): V

    final override fun createView(parent: ViewGroup): V {
        return createView(parent, parent.context)
    }

}